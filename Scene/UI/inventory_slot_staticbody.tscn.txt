# inventory_slot.tscn 重构说明
# 此文件描述了新的 StaticBody2D 版本的场景结构
# 请按照这个结构在 Godot 编辑器中重新创建 inventory_slot.tscn

[gd_scene load_steps=2 format=3 uid="uid://bwq8v5xhwxhxn"]

[ext_resource type="Script" path="res://Script/UI/inventory_slot.gd" id="1_abc123"]

[node name="InventorySlot" type="StaticBody2D"]
script = ExtResource("1_abc123")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
# 注意：需要在 Godot 编辑器中创建一个 RectangleShape2D
# 并设置 size = (64, 64)

[node name="SlotBackground" type="Sprite2D" parent="."]
# 这个纹理会在运行时动态生成，无需预设

[node name="ItemIcon" type="Sprite2D" parent="."]
# 物品图标，纹理会在运行时设置

[node name="QuantityLabel" type="Label" parent="."]
offset_left = 45.0
offset_top = 45.0
offset_right = 65.0
offset_bottom = 65.0
text = ""
horizontal_alignment = 1
vertical_alignment = 1

[node name="RarityBorder" type="Sprite2D" parent="."]
visible = false
# 稀有度边框，纹理会在运行时动态生成

# 创建步骤：
# 1. 在 Godot 编辑器中创建新场景
# 2. 添加 StaticBody2D 作为根节点，命名为 "InventorySlot"
# 3. 添加上述子节点
# 4. 为 CollisionShape2D 创建 RectangleShape2D，设置 size 为 (64, 64)
# 5. 设置 Label 的位置和对齐方式
# 6. 保存为 res://Scene/UI/inventory_slot.tscn