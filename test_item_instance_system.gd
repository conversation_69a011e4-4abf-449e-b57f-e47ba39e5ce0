extends Node

## 测试新的物品实例系统
## 验证模板和实例的正确分离

func _ready():
	print("=== 物品实例系统测试 ===")
	test_template_immutability()
	test_instance_independence()
	test_inventory_operations()
	test_tag_util_compatibility()
	test_error_handling()
	print("=== 测试完成 ===")

## 测试1：模板不可变性
func test_template_immutability():
	print("\n--- 测试1：模板不可变性 ---")
	
	# 初始化数据库
	ItemDatabase.initialize()
	
	# 获取面包模板
	var bread_template = ItemDatabase.get_item("bread")
	if not bread_template:
		print("❌ 无法获取bread模板")
		return
	
	var original_use = TagUtil.tagi(bread_template, "use", 0)
	print("✓ 面包模板原始use值: %d" % original_use)
	
	# 创建实例并使用
	var instance1 = ItemDatabase.create_item_instance("bread")
	var instance2 = ItemDatabase.create_item_instance("bread")
	
	print("✓ 实例1初始use值: %d" % instance1.get_remaining_uses())
	print("✓ 实例2初始use值: %d" % instance2.get_remaining_uses())
	
	# 使用实例1
	instance1.consume_use()
	print("✓ 实例1使用后use值: %d" % instance1.get_remaining_uses())
	print("✓ 实例2状态: %d" % instance2.get_remaining_uses())
	
	# 验证模板未被修改
	var template_use_after = TagUtil.tagi(bread_template, "use", 0)
	if template_use_after == original_use:
		print("✅ 模板保持不变: %d" % template_use_after)
	else:
		print("❌ 模板被意外修改: %d -> %d" % [original_use, template_use_after])

## 测试2：实例独立性
func test_instance_independence():
	print("\n--- 测试2：实例独立性 ---")
	
	# 创建多个面包实例
	var bread1 = ItemDatabase.create_item_instance("bread")
	var bread2 = ItemDatabase.create_item_instance("bread")
	var bread3 = ItemDatabase.create_item_instance("bread")
	
	print("✓ 面包1初始状态: %d uses" % bread1.get_remaining_uses())
	print("✓ 面包2初始状态: %d uses" % bread2.get_remaining_uses())
	print("✓ 面包3初始状态: %d uses" % bread3.get_remaining_uses())
	
	# 只使用面包1
	bread1.consume_use()
	
	print("✓ 面包1使用后: %d uses" % bread1.get_remaining_uses())
	print("✓ 面包2状态: %d uses" % bread2.get_remaining_uses())
	print("✓ 面包3状态: %d uses" % bread3.get_remaining_uses())
	
	# 验证实例独立性
	if bread1.get_remaining_uses() != bread2.get_remaining_uses():
		print("✅ 实例状态独立")
	else:
		print("❌ 实例状态相互影响")

## 测试3：背包操作
func test_inventory_operations():
	print("\n--- 测试3：背包操作 ---")
	
	# 清空背包
	InventoryManager.clear_inventory()
	
	# 添加面包到背包
	print("添加3个面包到背包...")
	InventoryManager.add_item("bread", 3)
	
	# 检查背包状态
	print("背包状态:")
	for i in range(5):
		var slot = InventoryManager.inventory_slots[i]
		if not slot.is_empty():
			var template = slot.get_template()
			print("  槽位%d: %s x%d" % [i, template.name, slot.get_quantity()])
			
			# 显示每个实例的状态
			for j in range(slot.item_instances.size()):
				var instance = slot.item_instances[j]
				print("    实例%d: 剩余使用次数=%d" % [j, instance.get_remaining_uses()])
	
	# 使用第一个面包
	print("\n使用第一个面包...")
	InventoryManager.use_item(0)
	
	# 再次检查背包状态
	print("\n使用后背包状态:")
	for i in range(5):
		var slot = InventoryManager.inventory_slots[i]
		if not slot.is_empty():
			var template = slot.get_template()
			print("  槽位%d: %s x%d" % [i, template.name, slot.get_quantity()])
			
			for j in range(slot.item_instances.size()):
				var instance = slot.item_instances[j]
				print("    实例%d: 剩余使用次数=%d" % [j, instance.get_remaining_uses()])

## 测试4：TagUtil兼容性
func test_tag_util_compatibility():
	print("\n--- 测试4：TagUtil兼容性 ---")
	
	# 测试模板访问
	var sword_template = ItemDatabase.get_item("sword_iron")
	if sword_template:
		var attack = TagUtil.tagi(sword_template, "attack", 0)
		var slot = TagUtil.tags(sword_template, "slot", "")
		print("✓ 模板访问 - 攻击力: %d, 槽位: %s" % [attack, slot])
	
	# 测试实例访问
	var sword_instance = ItemDatabase.create_item_instance("sword_iron")
	if sword_instance:
		var attack = TagUtil.tagi(sword_instance, "attack", 0)
		var slot = TagUtil.tags(sword_instance, "slot", "")
		var usable = TagUtil.is_usable(sword_instance)
		var equipable = TagUtil.is_equipable(sword_instance)
		print("✓ 实例访问 - 攻击力: %d, 槽位: %s" % [attack, slot])
		print("✓ 实例状态 - 可使用: %s, 可装备: %s" % [usable, equipable])
	
	# 测试消耗品实例
	var potion_instance = ItemDatabase.create_item_instance("potion_health")
	if potion_instance:
		print("✓ 药水实例初始状态: %d uses" % potion_instance.get_remaining_uses())
		potion_instance.consume_use()
		print("✓ 药水实例使用后: %d uses" % potion_instance.get_remaining_uses())
	
	print("✅ TagUtil兼容性测试通过")

## 测试5：错误处理
func test_error_handling():
	print("\n--- 测试5：错误处理 ---")
	
	# 尝试对模板调用consume_use（应该发出警告）
	var template = ItemDatabase.get_item("bread")
	if template:
		print("尝试对模板调用consume_use（应该发出警告）:")
		TagUtil.consume_use(template)
	
	print("✅ 错误处理测试完成")
