extends CanvasLayer

## 背包UI管理器
## 处理背包界面的显示、交互和更新

# ================================
# UI节点引用
# ================================

@onready var close_button: Button = %CloseButton
@onready var inventory_grid: GridContainer = %InventoryGrid
@onready var equipment_container: VBoxContainer = %EquipmentContainer
@onready var item_info: VBoxContainer = %ItemInfo
@onready var item_name: Label = %ItemName
@onready var item_description: Label = %ItemDescription
@onready var action_buttons: HBoxContainer = %ActionButtons
@onready var use_button: Button = %UseButton
@onready var drop_button: Button = %DropButton

# ================================
# 预制场景
# ================================

const INVENTORY_SLOT_SCENE = preload("res://Scene/UI/inventory_slot.tscn")

# ================================
# 状态变量
# ================================

var inventory_slot_buttons: Array = []  # 改为通用Array来存储InventorySlot节点
var equipment_slot_buttons: Dictionary = {}
var selected_slot_index: int = -1
var is_equipment_selected: bool = false
var selected_equipment_slot: InventoryManager.EquipmentSlot

# ================================
# 初始化
# ================================

func _ready():
	_setup_ui_connections()
	_setup_inventory_grid()
	_setup_equipment_slots()
	_update_display()
	
	# 连接背包管理器信号
	if InventoryManager:
		InventoryManager.inventory_changed.connect(_on_inventory_changed)
		InventoryManager.equipment_changed.connect(_on_equipment_changed)

func _setup_ui_connections():
	close_button.pressed.connect(_on_close_pressed)
	use_button.pressed.connect(_on_use_pressed)
	drop_button.pressed.connect(_on_drop_pressed)

func _setup_inventory_grid():
	# 清除现有槽位
	for slot_node in inventory_slot_buttons:
		if slot_node and is_instance_valid(slot_node):
			slot_node.queue_free()
	inventory_slot_buttons.clear()
	
	# 创建背包槽位（StaticBody2D版本需要特殊定位）
	if not InventoryManager:
		return
	
	var slots = InventoryManager.get_inventory_slots()
	
	# 等待一帧确保UI布局完成
	await get_tree().process_frame
	
	# 获取InventoryGrid的实际屏幕位置
	var grid_container = inventory_grid
	var grid_global_pos = grid_container.global_position
	var grid_size = grid_container.size
	
	# 调整起始位置到网格容器内部
	var grid_start_pos = grid_global_pos + Vector2(10, 10)  # 稍微偏移避免边界
	var slot_size = Vector2(70, 70)        # 槽位间距
	var cols = 6                           # 列数
	
	for i in range(slots.size()):
		var slot_node = INVENTORY_SLOT_SCENE.instantiate()
		slot_node.setup_slot(i, slots[i])
		
		# 计算网格位置（基于实际UI位置）
		var row = i / cols
		var col = i % cols
		var pos = grid_start_pos + Vector2(col * slot_size.x, row * slot_size.y)
		
		# StaticBody2D 直接添加到场景，不是UI容器
		add_child(slot_node)
		slot_node.global_position = pos
		
		# 连接信号（StaticBody2D版本使用不同的信号）
		slot_node.slot_clicked.connect(_on_inventory_slot_pressed)  # 移除bind，因为信号本身已经传递slot_index
		slot_node.drag_started.connect(_on_slot_drag_started)
		slot_node.drag_ended.connect(_on_slot_drag_ended)
		slot_node.item_dropped.connect(_on_item_dropped)
		
		inventory_slot_buttons.append(slot_node)

func _setup_equipment_slots():
	# 获取装备槽位面板
	var weapon_slot = equipment_container.get_node("WeaponSlot")
	var armor_slot = equipment_container.get_node("ArmorSlot")
	var accessory_slot = equipment_container.get_node("AccessorySlot")
	var shoes_slot = equipment_container.get_node("ShoesSlot")
	var helmet_slot = equipment_container.get_node("HelmetSlot")
	
	# 为每个槽位添加按钮
	_setup_equipment_slot_button(weapon_slot, InventoryManager.EquipmentSlot.WEAPON)
	_setup_equipment_slot_button(armor_slot, InventoryManager.EquipmentSlot.ARMOR)
	_setup_equipment_slot_button(accessory_slot, InventoryManager.EquipmentSlot.ACCESSORY)
	_setup_equipment_slot_button(shoes_slot, InventoryManager.EquipmentSlot.SHOES)
	_setup_equipment_slot_button(helmet_slot, InventoryManager.EquipmentSlot.HELMET)

func _setup_equipment_slot_button(slot_panel: Panel, equipment_slot: InventoryManager.EquipmentSlot):
	var button = Button.new()
	button.flat = true
	button.pressed.connect(_on_equipment_slot_pressed.bind(equipment_slot))
	
	# 设置按钮覆盖整个面板
	slot_panel.add_child(button)
	button.anchors_preset = Control.PRESET_FULL_RECT
	
	equipment_slot_buttons[equipment_slot] = button

# ================================
# 显示更新
# ================================

func _update_display():
	_update_inventory_display()
	_update_equipment_display()
	_update_item_info()

func _update_inventory_display():
	if not InventoryManager:
		return
	
	var slots = InventoryManager.get_inventory_slots()
	
	for i in range(min(slots.size(), inventory_slot_buttons.size())):
		var slot = slots[i]
		var slot_node = inventory_slot_buttons[i]
		
		# 只更新数据，不重新setup（避免递归调用）
		if slot_node.inventory_slot != slot:
			slot_node.inventory_slot = slot
			slot_node._update_display()
		
		# 高亮选中的槽位
		if i == selected_slot_index and not is_equipment_selected:
			slot_node.set_selected(true)
		else:
			slot_node.set_selected(false)

func _update_equipment_display():
	if not InventoryManager:
		return
	
	var equipped_items = InventoryManager.get_equipped_items()
	
	for slot in equipment_slot_buttons:
		var button = equipment_slot_buttons[slot]
		var equipment = equipped_items.get(slot)
		
		if equipment:
			button.text = equipment.name
			# button.icon = load(equipment.icon_path)
		else:
			button.text = ""
			button.icon = null
		
		# 高亮选中的装备槽位
		if is_equipment_selected and selected_equipment_slot == slot:
			button.modulate = Color.YELLOW
		else:
			button.modulate = Color.WHITE

func _update_item_info():
	if not InventoryManager:
		item_name.text = "无背包系统"
		item_description.text = ""
		_update_action_buttons(false, false)
		return
	
	var item_to_show = null
	var can_use = false
	var can_drop = false
	
	if is_equipment_selected:
		# 显示装备信息
		var equipped_items = InventoryManager.get_equipped_items()
		var equipment = equipped_items.get(selected_equipment_slot)
		if equipment:
			item_to_show = equipment
			can_drop = true  # 可以卸下装备
	elif selected_slot_index >= 0:
		# 显示背包物品信息
		var slots = InventoryManager.get_inventory_slots()
		if selected_slot_index < slots.size():
			var slot = slots[selected_slot_index]
			if not slot.is_empty():
				item_to_show = slot.item
				can_use = true
				can_drop = true
	
	if item_to_show:
		item_name.text = item_to_show.name
		var desc = item_to_show.description
		
		# 为装备添加属性信息
		if item_to_show is InventoryManager.Equipment:
			var equipment = item_to_show as InventoryManager.Equipment
			desc += "\n\n属性:"
			for stat in equipment.stats:
				desc += "\n%s: +%s" % [stat, equipment.stats[stat]]
			desc += "\n等级需求: %d" % equipment.level_requirement
		
		item_description.text = desc
	else:
		item_name.text = "选中物品信息"
		item_description.text = "请选择一个物品查看详细信息"
	
	_update_action_buttons(can_use, can_drop)

func _update_action_buttons(can_use: bool, can_drop: bool):
	use_button.disabled = not can_use
	drop_button.disabled = not can_drop
	
	if is_equipment_selected:
		use_button.text = "卸下"
	else:
		use_button.text = "使用"

# ================================
# 信号处理
# ================================

func _on_close_pressed():
	hide()

func _on_inventory_slot_pressed(slot_index: int):
	selected_slot_index = slot_index
	is_equipment_selected = false
	_update_display()

func _on_equipment_slot_pressed(equipment_slot: InventoryManager.EquipmentSlot):
	selected_equipment_slot = equipment_slot
	is_equipment_selected = true
	selected_slot_index = -1
	_update_display()

func _on_use_pressed():
	if not InventoryManager:
		return
	
	if is_equipment_selected:
		# 卸下装备
		InventoryManager.unequip_item(selected_equipment_slot)
	elif selected_slot_index >= 0:
		# 使用背包物品
		InventoryManager.use_item(selected_slot_index)

func _on_drop_pressed():
	if not InventoryManager:
		return
	
	if is_equipment_selected:
		# 卸下装备（同使用按钮）
		InventoryManager.unequip_item(selected_equipment_slot)
	elif selected_slot_index >= 0:
		# 丢弃背包物品
		var slots = InventoryManager.get_inventory_slots()
		if selected_slot_index < slots.size():
			var slot = slots[selected_slot_index]
			if not slot.is_empty():
				# 显示确认对话框
				_show_drop_confirmation(slot.item, slot.quantity)

func _show_drop_confirmation(item: InventoryManager.Item, quantity: int):
	# 创建简单的确认对话框
	var dialog = AcceptDialog.new()
	dialog.dialog_text = "确定要丢弃 %d x %s 吗？" % [quantity, item.name]
	dialog.title = "确认丢弃"
	
	# 添加取消按钮
	dialog.add_cancel_button("取消")
	
	get_tree().current_scene.add_child(dialog)
	dialog.popup_centered()
	
	var result = await dialog.confirmed
	if result:
		# 确认丢弃，移除物品
		InventoryManager.remove_item(item.id, quantity)
		_logger("Dropped %d x %s" % [quantity, item.name])
	
	dialog.queue_free()

func _on_inventory_changed():
	_update_display()

func _on_equipment_changed(_slot: InventoryManager.EquipmentSlot, _old_equipment: InventoryManager.Equipment, _new_equipment: InventoryManager.Equipment):
	_update_display()

# ================================
# 拖拽相关信号处理
# ================================

func _on_slot_drag_started(slot_index: int):
	_logger("开始拖拽槽位 %d" % slot_index)

func _on_slot_drag_ended(slot_index: int):
	_logger("结束拖拽槽位 %d" % slot_index)
	_update_display()

func _on_item_dropped(from_slot: int, to_slot: int):
	_logger("物品从槽位 %d 移动到槽位 %d" % [from_slot, to_slot])
	_update_display()

# ================================
# 公共接口
# ================================

func show_inventory():
	show()
	_update_display()

func hide_inventory():
	hide()
	selected_slot_index = -1
	is_equipment_selected = false

# ================================
# 输入处理
# ================================

func _input(event):
	if not visible:
		return
	
	if event.is_action_pressed("menu"):
		hide_inventory()
		get_viewport().set_input_as_handled()

# ================================
# 工具函数
# ================================

func _logger(msg: String) -> void:
	print("[InventoryUI] %s" % msg)
