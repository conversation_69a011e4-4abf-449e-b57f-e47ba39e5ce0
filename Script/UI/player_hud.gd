extends CanvasLayer

## 玩家HUD显示组件
## 实时显示玩家状态信息，自动跟随场景切换

@onready var hp_value: Label = %HPValue
@onready var coins_value: Label = %CoinsValue
@onready var level_value: Label = %LevelValue
@onready var experience_value: Label = %ExperienceValue
@onready var inventory_button: Button = %InventoryButton

var update_timer: Timer
var inventory_ui: CanvasLayer = null

func _ready():
	# 设置处理模式确保在所有场景都能工作
	process_mode = Node.PROCESS_MODE_ALWAYS
	
	# 创建更新计时器
	update_timer = Timer.new()
	update_timer.wait_time = 0.1  # 每0.1秒更新一次
	update_timer.timeout.connect(_update_hud)
	update_timer.autostart = true
	add_child(update_timer)
	
	# 连接背包按钮
	if inventory_button:
		inventory_button.pressed.connect(_on_inventory_button_pressed)
	
	# 连接状态管理器信号以控制显示/隐藏
	if AppStateManager:
		AppStateManager.state_changed.connect(_on_app_state_changed)
	
	# 根据初始状态设置可见性
	_update_visibility_for_current_state()
	
	# 初始更新
	_update_hud()
	
	_logger("PlayerHUD ready")

## 根据应用状态控制HUD显示/隐藏
func _on_app_state_changed(_old_state: AppStateManager.AppState, new_state: AppStateManager.AppState):
	_update_visibility_for_state(new_state)

## 根据状态更新可见性
func _update_visibility_for_state(state: AppStateManager.AppState):
	var should_show = state == AppStateManager.AppState.IN_GAME
	set_hud_visible(should_show)

## 根据当前状态更新可见性
func _update_visibility_for_current_state():
	if AppStateManager:
		_update_visibility_for_state(AppStateManager.get_current_state())

## 更新HUD显示
func _update_hud():
	if not GameManager:
		return
	
	var player_data = GameManager.get_player_data()
	
	# 更新HP显示
	if hp_value:
		hp_value.text = "%d/%d" % [player_data.get("hp", 0), player_data.get("max_hp", 100)]
	
	# 更新金币显示
	if coins_value:
		coins_value.text = str(player_data.get("coins", 0))
	
	# 更新等级显示
	if level_value:
		level_value.text = str(player_data.get("level", 1))
	
	# 更新经验显示
	if experience_value:
		experience_value.text = str(player_data.get("experience", 0))

## 手动刷新HUD（供外部调用）
func refresh_hud():
	_update_hud()

## 显示/隐藏HUD
func set_hud_visible(hud_visible: bool):
	self.visible = hud_visible

## 背包按钮点击处理
func _on_inventory_button_pressed():
	# 使用新的独立背包页面
	if GameManager:
		GameManager.open_inventory()

## 切换背包显示（保留但不使用，用于备用浮动UI）
func _toggle_inventory():
	if inventory_ui and is_instance_valid(inventory_ui):
		if inventory_ui.visible:
			inventory_ui.hide_inventory()
		else:
			inventory_ui.show_inventory()

## 输入处理
func _input(event):
	if not visible:
		return
	
	# 检查背包快捷键 (I键) - 现在使用独立页面
	if event.is_action_pressed("inventory"):
		if GameManager:
			GameManager.open_inventory()
		get_viewport().set_input_as_handled()

## 统一日志输出
func _logger(msg: String):
	print("[PlayerHUD] %s" % msg)
