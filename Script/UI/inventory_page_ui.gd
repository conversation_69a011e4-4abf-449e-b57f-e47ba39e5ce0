extends CanvasLayer

## 独立的背包页面UI
## 作为全屏页面显示，会暂停游戏，类似设置菜单

# ================================
# UI节点引用
# ================================

@onready var close_button: Button = %CloseButton
@onready var inventory_grid: GridContainer = %InventoryGrid
@onready var equipment_container: VBoxContainer = %EquipmentContainer
@onready var item_info: VBoxContainer = %ItemInfo
@onready var item_name: Label = %ItemName
@onready var item_description: Label = %ItemDescription
@onready var action_buttons: HBoxContainer = %ActionButtons
@onready var use_button: Button = %UseButton
@onready var drop_button: Button = %DropButton
@onready var organize_button: Button = %OrganizeButton

# ================================
# 预制场景
# ================================

const INVENTORY_SLOT_SCENE = preload("res://Scene/UI/inventory_slot.tscn")

# ================================
# 状态变量
# ================================

var inventory_slot_buttons: Array = []
var equipment_slot_buttons: Dictionary = {}
var selected_slot_index: int = -1
var is_equipment_selected: bool = false
var selected_equipment_slot: InventoryManager.EquipmentSlot

# ================================
# 初始化
# ================================

func _ready():
	# 设置处理模式，使其在游戏暂停时也能正常工作
	process_mode = Node.PROCESS_MODE_ALWAYS
	
	# 设置更高的输入处理优先级
	set_process_input(true)
	set_process_unhandled_input(false)
	
	_setup_ui_connections()
	_setup_inventory_grid()
	_setup_equipment_slots()
	_update_display()
	
	# 连接背包管理器信号
	if InventoryManager:
		InventoryManager.inventory_changed.connect(_on_inventory_changed)
		InventoryManager.equipment_changed.connect(_on_equipment_changed)

# 处理ESC键返回
func _input(event):
	if not visible or not is_inside_tree():
		return

	# 只处理ESC键 - 让其他输入正常流动到UI控件
	if event is InputEventKey and event.pressed and event.keycode == KEY_ESCAPE:
		# 先阻止事件传播
		get_viewport().set_input_as_handled()
		# 然后处理关闭逻辑
		_on_close_pressed()
		return

func _setup_ui_connections():
	close_button.pressed.connect(_on_close_pressed)
	use_button.pressed.connect(_on_use_pressed)
	drop_button.pressed.connect(_on_drop_pressed)
	organize_button.pressed.connect(_on_organize_pressed)

func _setup_inventory_grid():
	# 清除现有槽位
	for child in inventory_grid.get_children():
		child.queue_free()
	inventory_slot_buttons.clear()
	
	# 创建背包槽位
	if not InventoryManager:
		return
	
	var slots = InventoryManager.get_inventory_slots()
	for i in range(slots.size()):
		var slot_node = INVENTORY_SLOT_SCENE.instantiate()
		slot_node.setup_slot(i, slots[i])
		slot_node.pressed.connect(_on_inventory_slot_pressed.bind(i))
		
		inventory_grid.add_child(slot_node)
		inventory_slot_buttons.append(slot_node)

func _setup_equipment_slots():
	# 获取装备槽位面板
	var weapon_slot = equipment_container.get_node("WeaponSlot")
	var armor_slot = equipment_container.get_node("ArmorSlot")
	var accessory_slot = equipment_container.get_node("AccessorySlot")
	var shoes_slot = equipment_container.get_node("ShoesSlot")
	var helmet_slot = equipment_container.get_node("HelmetSlot")
	
	# 为每个槽位添加按钮
	_setup_equipment_slot_button(weapon_slot, InventoryManager.EquipmentSlot.WEAPON)
	_setup_equipment_slot_button(armor_slot, InventoryManager.EquipmentSlot.ARMOR)
	_setup_equipment_slot_button(accessory_slot, InventoryManager.EquipmentSlot.ACCESSORY)
	_setup_equipment_slot_button(shoes_slot, InventoryManager.EquipmentSlot.SHOES)
	_setup_equipment_slot_button(helmet_slot, InventoryManager.EquipmentSlot.HELMET)

func _setup_equipment_slot_button(slot_panel: Panel, equipment_slot: InventoryManager.EquipmentSlot):
	var button = Button.new()
	button.flat = true
	button.pressed.connect(_on_equipment_slot_pressed.bind(equipment_slot))
	
	# 设置按钮覆盖整个面板
	slot_panel.add_child(button)
	button.anchors_preset = Control.PRESET_FULL_RECT
	
	equipment_slot_buttons[equipment_slot] = button

# ================================
# 显示更新
# ================================

func _update_display():
	_update_inventory_display()
	_update_equipment_display()
	_update_item_info()

func _update_inventory_display():
	if not InventoryManager:
		return
	
	var slots = InventoryManager.get_inventory_slots()
	
	for i in range(min(slots.size(), inventory_slot_buttons.size())):
		var slot = slots[i]
		var slot_node = inventory_slot_buttons[i]
		
		# 更新槽位数据
		slot_node.setup_slot(i, slot)
		
		# 高亮选中的槽位
		if i == selected_slot_index and not is_equipment_selected:
			slot_node.set_selected(true)
		else:
			slot_node.set_selected(false)

func _update_equipment_display():
	if not InventoryManager:
		return
	
	var equipped_items = InventoryManager.get_equipped_items()
	
	for slot in equipment_slot_buttons:
		var button = equipment_slot_buttons[slot]
		var equipment = equipped_items.get(slot)
		
		if equipment:
			button.text = equipment.name
		else:
			button.text = ""
		
		# 高亮选中的装备槽位
		if is_equipment_selected and selected_equipment_slot == slot:
			button.modulate = Color.YELLOW
		else:
			button.modulate = Color.WHITE

func _update_item_info():
	if not InventoryManager:
		item_name.text = "无背包系统"
		item_description.text = ""
		_update_action_buttons(false, false)
		return
	
	var item_to_show = null
	var can_use = false
	var can_drop = false
	
	if is_equipment_selected:
		# 显示装备信息
		var equipped_items = InventoryManager.get_equipped_items()
		var equipment = equipped_items.get(selected_equipment_slot)
		if equipment:
			item_to_show = equipment
			can_drop = true  # 可以卸下装备
	elif selected_slot_index >= 0:
		# 显示背包物品信息
		var slots = InventoryManager.get_inventory_slots()
		if selected_slot_index < slots.size():
			var slot = slots[selected_slot_index]
			if not slot.is_empty():
				item_to_show = slot.get_template()
				can_use = true
				can_drop = true
	
	if item_to_show:
		item_name.text = item_to_show.name
		var desc = item_to_show.description
		
		# 为装备添加属性信息
		if item_to_show is InventoryManager.Equipment:
			var equipment = item_to_show as InventoryManager.Equipment
			desc += "\n\n属性:"
			for stat in equipment.stats:
				desc += "\n%s: +%s" % [stat, equipment.stats[stat]]
			desc += "\n等级需求: %d" % equipment.level_requirement
		
		item_description.text = desc
	else:
		item_name.text = "选中物品信息"
		item_description.text = "请选择一个物品查看详细信息"
	
	_update_action_buttons(can_use, can_drop)

func _update_action_buttons(can_use: bool, can_drop: bool):
	use_button.disabled = not can_use
	drop_button.disabled = not can_drop
	
	if is_equipment_selected:
		use_button.text = "卸下"
	else:
		use_button.text = "使用"

# ================================
# 信号处理
# ================================

func _on_close_pressed():
	# 使用AppStateManager返回游戏
	if AppStateManager:
		AppStateManager.back_from_inventory()

func _on_inventory_slot_pressed(slot_index: int):
	selected_slot_index = slot_index
	is_equipment_selected = false
	_update_display()

func _on_equipment_slot_pressed(equipment_slot: InventoryManager.EquipmentSlot):
	selected_equipment_slot = equipment_slot
	is_equipment_selected = true
	selected_slot_index = -1
	_update_display()

func _on_use_pressed():
	if not InventoryManager:
		return
	
	if is_equipment_selected:
		# 卸下装备
		InventoryManager.unequip_item(selected_equipment_slot)
	elif selected_slot_index >= 0:
		# 检查选中物品是否为装备
		var slots = InventoryManager.get_inventory_slots()
		var slot = slots[selected_slot_index]
		var instance = slot.get_first_instance()
		if instance and instance.is_equipable():
			InventoryManager.equip_item(selected_slot_index)
		else:
			InventoryManager.use_item(selected_slot_index)

func _on_drop_pressed():
	if not InventoryManager:
		return
	
	if is_equipment_selected:
		# 卸下装备（同使用按钮）
		InventoryManager.unequip_item(selected_equipment_slot)
	elif selected_slot_index >= 0:
		# 丢弃背包物品
		var slots = InventoryManager.get_inventory_slots()
		if selected_slot_index < slots.size():
			var slot = slots[selected_slot_index]
			if not slot.is_empty():
				# 显示确认对话框
				_show_drop_confirmation(slot.get_template(), slot.get_quantity())

func _show_drop_confirmation(item: InventoryManager.Item, quantity: int):
	# 创建简单的确认对话框
	var dialog = AcceptDialog.new()
	dialog.dialog_text = "确定要丢弃 %d x %s 吗？" % [quantity, item.name]
	dialog.title = "确认丢弃"
	
	# 添加取消按钮
	dialog.add_cancel_button("取消")
	
	add_child(dialog)
	dialog.popup_centered()
	
	# 连接确认信号
	dialog.confirmed.connect(_on_drop_confirmed.bind(item, quantity, dialog))
	dialog.canceled.connect(_on_drop_canceled.bind(dialog))

func _on_drop_confirmed(item: InventoryManager.Item, quantity: int, dialog: AcceptDialog):
	# 确认丢弃，从选中的槽位移除物品
	InventoryManager.remove_item_from_slot(selected_slot_index, quantity)
	_logger("Dropped %d x %s from slot %d" % [quantity, item.name, selected_slot_index])
	dialog.queue_free()

func _on_drop_canceled(dialog: AcceptDialog):
	dialog.queue_free()

func _on_inventory_changed():
	_update_display()

func _on_equipment_changed(_slot: InventoryManager.EquipmentSlot, _old_equipment: InventoryManager.Equipment, _new_equipment: InventoryManager.Equipment):
	_update_display()

func _on_organize_pressed():
	if InventoryManager:
		InventoryManager.organize_inventory()
		_logger("Inventory organized")

# ================================
# 公共接口
# ================================

func show_inventory_page():
	show()
	_update_display()
	selected_slot_index = -1
	is_equipment_selected = false

func hide_inventory_page():
	hide()

# ================================
# 工具函数
# ================================

func _logger(msg: String) -> void:
	print("[InventoryPageUI] %s" % msg)
