extends Node

## StaticBody2D inventory_slot 重构测试
## 验证新的拖拽功能是否正确工作

func _ready():
	print("[InventorySlotTest] 开始测试 StaticBody2D 重构...")
	_test_slot_creation()
	_test_drag_functionality()
	_test_compatibility()

func _test_slot_creation():
	print("[Test] 测试槽位创建...")
	
	# 这个测试需要在场景文件重新创建后进行
	# var slot_scene = preload("res://Scene/UI/inventory_slot.tscn")
	# var slot = slot_scene.instantiate()
	# print("槽位类型: %s" % slot.get_class())
	# slot.queue_free()

func _test_drag_functionality():
	print("[Test] 测试拖拽功能...")
	
	# 验证拖拽相关的枚举和常量
	var slot = preload("res://Script/UI/inventory_slot.gd").new()
	print("DragState 枚举定义正确: %s" % (slot.DragState.IDLE == 0))
	print("拖拽阈值设置: %s 像素" % slot.DRAG_THRESHOLD)
	print("点击阈值设置: %s 秒" % slot.CLICK_THRESHOLD)
	slot.queue_free()

func _test_compatibility():
	print("[Test] 测试兼容性接口...")
	
	# 验证关键方法是否存在
	var slot = preload("res://Script/UI/inventory_slot.gd").new()
	var methods = [
		"setup_slot",
		"set_selected", 
		"set_highlighted",
		"get_slot_index",
		"is_empty",
		"move_item_to_slot"
	]
	
	for method in methods:
		if slot.has_method(method):
			print("✓ 方法 %s 存在" % method)
		else:
			print("✗ 方法 %s 缺失" % method)
	
	slot.queue_free()
	print("[InventorySlotTest] 测试完成!")