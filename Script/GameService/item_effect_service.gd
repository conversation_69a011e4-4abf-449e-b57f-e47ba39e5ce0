extends Node

## 物品效果服务 - 处理物品使用和装备效果
## 订阅InventoryManager信号，根据tag分发效果到StatService

class_name ItemEffectService

# ================================
# 依赖注入
# ================================

var stat_service: StatService
var inventory_manager: Node

# ================================
# 信号定义
# ================================

signal effect_applied(item: InventoryManager.Item, effect_type: String, value)
signal effect_failed(item: InventoryManager.Item, reason: String)

# ================================
# 初始化
# ================================

func _ready():
	# 等待其他服务初始化完成
	call_deferred("_initialize_service")

func _initialize_service():
	if not _setup_dependencies():
		print("[ItemEffectService] Failed to setup dependencies, service initialization aborted")
		return
	
	_register_tag_handlers()
	_connect_inventory_signals()
	print("[ItemEffectService] ItemEffectService initialized")

func _setup_dependencies():
	# 获取StatService引用（从GameManager）
	if GameManager and GameManager.has_method("get") and GameManager.stat_service:
		stat_service = GameManager.stat_service
		print("[ItemEffectService] StatService reference obtained from GameManager")
	else:
		print("[ItemEffectService] Warning: StatService not found in GameManager")
		return false
	
	# 获取InventoryManager引用（autoload）
	inventory_manager = get_node_or_null("/root/InventoryManager")
	if not inventory_manager:
		print("[ItemEffectService] Warning: InventoryManager not found")
		return false
	
	return true

func _register_tag_handlers():
	# 注册TagUtil的默认处理器，并传入StatService引用
	if stat_service:
		TagUtil.register_default_handlers(stat_service)
	
	# 注册自定义处理器
	_register_custom_handlers()

func _register_custom_handlers():
	# 价值相关（暂时只打印，实际可能用于商店等）
	TagUtil.register_handler("value", func(_service, _item, value):
		print("[ItemEffectService] Item value: %d" % value)
	)
	
	# 稀有度（主要用于UI显示，这里记录日志）
	TagUtil.register_handler("rarity", func(_service, _item, value):
		print("[ItemEffectService] Item rarity level: %d" % value)
	)
	
	# 任务相关物品
	TagUtil.register_handler("quest_id", func(_service, item, value):
		_handle_quest_item(item, value)
	)
	
	# 时效性物品（未来扩展）
	TagUtil.register_handler("decay_sec", func(_service, _item, value):
		print("[ItemEffectService] Item will decay in %d seconds" % value)
	)
	
	print("[ItemEffectService] Custom handlers registered")

func _connect_inventory_signals():
	if inventory_manager:
		# 连接物品使用信号
		if inventory_manager.has_signal("item_used"):
			inventory_manager.item_used.connect(_on_item_used)
		
		# 连接装备变化信号
		if inventory_manager.has_signal("equipment_changed"):
			inventory_manager.equipment_changed.connect(_on_equipment_changed)
		
		print("[ItemEffectService] Connected to inventory signals")
	else:
		print("[ItemEffectService] Cannot connect signals: InventoryManager not found")

# ================================
# 信号处理函数
# ================================

## 处理物品使用事件
func _on_item_used(item: InventoryManager.Item):
	if not item:
		return

	print("[ItemEffectService] Processing item use: %s" % item.name)

	# 检查是否可使用
	if not TagUtil.is_usable(item):
		print("[ItemEffectService] Item is not usable: %s" % item.name)
		effect_failed.emit(item, "Item is not usable")
		return

	# 应用使用效果
	if stat_service:
		TagUtil.apply_use_effects(stat_service, item)
		effect_applied.emit(item, "use", 1)
	else:
		print("[ItemEffectService] Cannot apply effects: StatService not available")
		effect_failed.emit(item, "StatService not available")

	print("[ItemEffectService] Item use processed: %s" % item.name)

## 处理装备变化事件
func _on_equipment_changed(slot: InventoryManager.EquipmentSlot, old_equipment: InventoryManager.Item, new_equipment: InventoryManager.Item):
	print("[ItemEffectService] Processing equipment change in slot %s" % InventoryManager.EquipmentSlot.keys()[slot])

	# 移除旧装备效果
	if old_equipment:
		_remove_equipment_effects(old_equipment)

	# 应用新装备效果
	if new_equipment:
		_apply_equipment_effects(new_equipment)

func _apply_equipment_effects(equipment: InventoryManager.Item):
	if not equipment or not stat_service:
		return

	print("[ItemEffectService] Applying equipment effects: %s" % equipment.name)

	# 使用TagUtil应用装备效果
	TagUtil.apply_equipment_effects(stat_service, equipment, true)

	effect_applied.emit(equipment, "equip", 1)
	print("[ItemEffectService] Equipment effects applied: %s" % equipment.name)

func _remove_equipment_effects(equipment: InventoryManager.Item):
	if not equipment or not stat_service:
		return

	print("[ItemEffectService] Removing equipment effects: %s" % equipment.name)

	# 使用TagUtil移除装备效果
	TagUtil.apply_equipment_effects(stat_service, equipment, false)

	effect_applied.emit(equipment, "unequip", -1)
	print("[ItemEffectService] Equipment effects removed: %s" % equipment.name)

# ================================
# 特殊效果处理
# ================================

## 处理任务物品
func _handle_quest_item(item: InventoryManager.Item, quest_id: String):
	print("[ItemEffectService] Quest item used: %s (Quest ID: %s)" % [item.name, quest_id])
	
	# 这里可以集成任务系统
	# 例如：QuestManager.trigger_quest_event(quest_id, "item_used", item)
	
	# 暂时只输出日志
	print("[ItemEffectService] TODO: Integrate with quest system for quest %s" % quest_id)

## 处理特殊消耗品效果
func apply_custom_consumable_effect(item: InventoryManager.Item, effect_name: String, parameters: Dictionary):
	match effect_name:
		"full_heal":
			if stat_service:
				var max_hp = stat_service.get_stat("max_hp", 100)
				stat_service.set_stat("hp", max_hp)
				print("[ItemEffectService] Full heal applied")
		
		"full_restore":
			if stat_service:
				var max_hp = stat_service.get_stat("max_hp", 100)
				var max_mp = stat_service.get_stat("max_mp", 50)
				stat_service.set_stat("hp", max_hp)
				stat_service.set_stat("mp", max_mp)
				print("[ItemEffectService] Full restore applied")
		
		"temporary_buff":
			_apply_temporary_buff(item, parameters)
		
		_:
			print("[ItemEffectService] Unknown custom effect: %s" % effect_name)

## 应用临时增益效果（可扩展为BuffService）
func _apply_temporary_buff(item: InventoryManager.Item, parameters: Dictionary):
	var duration = parameters.get("duration", 30.0)
	var stat_name = parameters.get("stat", "attack")
	var bonus = parameters.get("bonus", 5)
	
	print("[ItemEffectService] Applying temporary buff: +%d %s for %d seconds" % [bonus, stat_name, duration])
	
	# 应用增益
	if stat_service:
		stat_service.modify_stat(stat_name, bonus)
	
	# 设置定时器移除增益
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = duration
	timer.one_shot = true
	timer.timeout.connect(func():
		if stat_service:
			stat_service.modify_stat(stat_name, -bonus)
		print("[ItemEffectService] Temporary buff expired: -%d %s" % [bonus, stat_name])
		timer.queue_free()
	)
	timer.start()

# ================================
# 批量效果处理
# ================================

## 批量应用多个物品效果（用于组合道具等）
func apply_multiple_item_effects(items: Array[InventoryManager.Item]):
	for item in items:
		if TagUtil.is_usable(item):
			_on_item_used(item)

## 应用装备套装效果
func apply_equipment_set_effects(equipment_set: Array[InventoryManager.Item]):
	# 检查是否为完整套装
	var set_ids = {}
	for equipment in equipment_set:
		if equipment:
			var set_id = TagUtil.tags(equipment, "set_id", "")
			if not set_id.is_empty():
				set_ids[set_id] = set_ids.get(set_id, 0) + 1
	
	# 应用套装效果
	for set_id in set_ids:
		var count = set_ids[set_id]
		_apply_set_bonus(set_id, count)

func _apply_set_bonus(set_id: String, piece_count: int):
	print("[ItemEffectService] Applying set bonus: %s (%d pieces)" % [set_id, piece_count])
	
	# 这里可以根据套装ID和件数应用不同的套装效果
	# 例如：基础套装2件+5攻击，4件+10攻击+5防御
	match set_id:
		"warrior_set":
			if piece_count >= 2 and stat_service:
				stat_service.modify_stat("attack", 5)
			if piece_count >= 4 and stat_service:
				stat_service.modify_stat("attack", 5)  # 额外+5
				stat_service.modify_stat("defense", 5)
		_:
			print("[ItemEffectService] Unknown equipment set: %s" % set_id)

# 验证代码已简化，移除过度的安全检查

# ================================
# 调试工具
# ================================

## 调试打印服务状态
func debug_print_status():
	print("[ItemEffectService] === ItemEffectService Status ===")
	print("  StatService: %s" % ("OK" if stat_service else "Missing"))
	print("  InventoryManager: %s" % ("OK" if inventory_manager else "Missing"))
	print("  Registered handlers: %s" % TagUtil.get_registered_handlers())
