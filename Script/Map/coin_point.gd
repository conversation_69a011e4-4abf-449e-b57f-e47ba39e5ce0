extends Area2D

## 金币收集点 - 玩家触碰后收集金币，消失后重新出现，带旋转动画

@onready var sprite: Sprite2D = $Sprite2D
@onready var collision_shape: CollisionShape2D = $CollisionShape2D

## 金币配置
const COIN_VALUE = 10
const RESPAWN_TIME = 1.0
const ROTATION_SPEED = 180.0  # 度/秒
const FLOAT_AMPLITUDE = 10.0  # 浮动幅度
const FLOAT_SPEED = 2.0  # 浮动速度

## 状态控制
var is_collected = false
var respawn_timer: Timer
var rotation_tween: Tween
var float_tween: Tween
var original_position: Vector2

func _ready():
	# 连接触发信号
	body_entered.connect(_on_body_entered)
	
	# 保存原始位置
	original_position = sprite.position
	
	# 设置动画
	_start_animations()
	
	# 创建重生计时器
	respawn_timer = Timer.new()
	respawn_timer.wait_time = RESPAWN_TIME
	respawn_timer.one_shot = true
	respawn_timer.timeout.connect(_on_respawn_timer_timeout)
	add_child(respawn_timer)
	
	_logger("CoinPoint ready at position: %s" % global_position)

## 开始所有动画
func _start_animations():
	_start_rotation_animation()
	_start_float_animation()

## 开始旋转动画
func _start_rotation_animation():
	if rotation_tween:
		rotation_tween.kill()
	
	rotation_tween = create_tween()
	rotation_tween.set_loops()  # 无限循环
	rotation_tween.tween_property(sprite, "rotation", TAU, 2.0)  # 2秒转一圈
	rotation_tween.tween_callback(func(): sprite.rotation = 0.0)

## 开始浮动动画
func _start_float_animation():
	if float_tween:
		float_tween.kill()
	
	float_tween = create_tween()
	float_tween.set_loops()  # 无限循环
	float_tween.tween_property(sprite, "position", original_position + Vector2(0, -FLOAT_AMPLITUDE), FLOAT_SPEED / 2.0)
	float_tween.tween_property(sprite, "position", original_position + Vector2(0, FLOAT_AMPLITUDE), FLOAT_SPEED)
	float_tween.tween_property(sprite, "position", original_position, FLOAT_SPEED / 2.0)

## 玩家进入触发区域
func _on_body_entered(body: Node2D):
	# 检查是否是玩家且未被收集
	if body.name == "TestRunner" and not is_collected:
		_collect_coin()

## 收集金币
func _collect_coin():
	if is_collected:
		return
	
	is_collected = true
	
	# 通过服务层增加玩家金币
	if GameManager and GameManager.stat_service:
		var current_coins = GameManager.stat_service.get_stat("coins", 0)
		GameManager.stat_service.add_coins(COIN_VALUE)
		var new_coins = GameManager.stat_service.get_stat("coins", 0)
		_logger("Player collected %d coins! Total: %d" % [COIN_VALUE, new_coins])
	else:
		_logger("Cannot add coins: StatService not available")
	
	# 播放收集效果
	_play_collect_effect()
	
	# 隐藏金币
	_hide_coin()
	
	# 开始重生计时器
	respawn_timer.start()

## 播放收集效果
func _play_collect_effect():
	# 停止所有动画
	if rotation_tween:
		rotation_tween.kill()
	if float_tween:
		float_tween.kill()
	
	# 创建收集动画（放大后缩小）
	var collect_tween = create_tween()
	collect_tween.set_parallel(true)
	
	# 放大效果
	collect_tween.tween_property(sprite, "scale", Vector2(0.8, 0.8), 0.1)
	# 淡出效果
	collect_tween.tween_property(sprite, "modulate", Color(1, 1, 1, 0), 0.3)

## 隐藏金币
func _hide_coin():
	sprite.visible = false
	collision_shape.call_deferred("set_disabled", true)

## 显示金币
func _show_coin():
	sprite.visible = true
	sprite.scale = Vector2(0.5, 0.5)  # 重置缩放
	sprite.modulate = Color(1, 1, 1, 1)  # 重置透明度
	sprite.position = original_position  # 重置位置
	sprite.rotation = 0.0  # 重置旋转
	collision_shape.call_deferred("set_disabled", false)
	
	# 重新开始动画
	_start_animations()
	
	# 播放出现效果
	var appear_tween = create_tween()
	appear_tween.tween_property(sprite, "scale", Vector2(0.6, 0.6), 0.2)
	appear_tween.tween_property(sprite, "scale", Vector2(0.5, 0.5), 0.1)

## 重生计时器超时
func _on_respawn_timer_timeout():
	is_collected = false
	_show_coin()
	_logger("CoinPoint respawned")

## 统一日志输出
func _logger(msg: String):
	print("[CoinPoint] %s" % msg)
