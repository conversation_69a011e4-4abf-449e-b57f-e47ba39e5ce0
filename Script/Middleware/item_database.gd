class_name ItemDatabase

## 物品数据库 - 从JSON加载物品定义并提供查询接口
## 作为数据层，支持运行时热更新

# ================================
# 静态数据存储
# ================================

static var _db: Dictionary = {}
static var _initialized: bool = false
static var _data_path: String = "res://data/items.json"

# ================================
# 初始化
# ================================

## 初始化数据库（在需要时调用）
static func initialize():
	if not _initialized:
		load_database()

## 加载物品数据库
static func load_database(file_path: String = "") -> bool:
	if not file_path.is_empty():
		_data_path = file_path
	
	print("[ItemDatabase] Loading items from: %s" % _data_path)
	
	# 检查文件是否存在
	if not FileAccess.file_exists(_data_path):
		print("[ItemDatabase] Error: File not found: %s" % _data_path)
		return false
	
	# 读取JSON文件
	var file = FileAccess.open(_data_path, FileAccess.READ)
	if not file:
		print("[ItemDatabase] Error: Cannot open file: %s" % _data_path)
		return false
	
	var json_text = file.get_as_text()
	file.close()
	
	# 解析JSON
	var json = JSON.new()
	var parse_result = json.parse(json_text)
	if parse_result != OK:
		print("[ItemDatabase] Error: JSON parse error at line %d: %s" % [json.get_error_line(), json.get_error_message()])
		return false
	
	var items_data = json.data
	if not items_data is Array:
		print("[ItemDatabase] Error: JSON root must be an array")
		return false
	
	# 清空现有数据库
	_db.clear()
	
	# 加载每个物品
	var loaded_count = 0
	for item_data in items_data:
		if _load_item_from_data(item_data):
			loaded_count += 1
		else:
			print("[ItemDatabase] Warning: Failed to load item: %s" % (item_data["id"] if item_data.has("id") else "unknown"))
	
	_initialized = true
	print("[ItemDatabase] Database loaded successfully: %d items" % loaded_count)
	
	return true

## 从字典数据创建物品对象
static func _load_item_from_data(data: Dictionary) -> bool:
	if not data.has("id") or not data.has("name"):
		print("[ItemDatabase] Error: Item missing required fields (id, name)")
		return false
	
	var item_id = data["id"]
	var item_type = data["type"] if data.has("type") else InventoryManager.ItemType.MISC
	
	# 创建物品对象（统一使用Item类）
	var item = InventoryManager.Item.new()

	# 如果是装备类型，确保标签中包含装备信息
	if item_type == InventoryManager.ItemType.EQUIPMENT:
		var tags_dict = TagUtil.parse(data["tags"] if data.has("tags") else "")
		# 确保有slot标签
		if not tags_dict.has("slot"):
			tags_dict["slot"] = "weapon"
		# 确保有level_req标签
		if not tags_dict.has("level_req"):
			tags_dict["level_req"] = data["level_requirement"] if data.has("level_requirement") else 1
		# 更新tags
		item.tags = tags_dict
		
		# 从tags解析stats（向后兼容）
		item.stats = _extract_stats_from_tags(data["tags"] if data.has("tags") else "")
	else:
		item = InventoryManager.Item.new()
	
	# 设置基础属性
	item.id = item_id
	item.name = data["name"] if data.has("name") else ""
	item.description = data["description"] if data.has("description") else ""
	item.icon_path = data["icon_path"] if data.has("icon_path") else ""
	item.type = item_type
	item.stack_size = data["stack_size"] if data.has("stack_size") else 1
	item.value = data["value"] if data.has("value") else 0
	item.rarity = data["rarity"] if data.has("rarity") else 0

	# 设置tags（新的标签系统）
	var tags_string = data["tags"] if data.has("tags") else ""
	if typeof(tags_string) == TYPE_STRING:
		item["tags"] = TagUtil.parse(tags_string)
	else:
		item["tags"] = tags_string
	
	_db[item_id] = item
	return true

# _string_to_equipment_slot函数已移除，现在使用TagUtil.get_equipment_slot()

## 从tags字符串提取stats字典（向后兼容）
static func _extract_stats_from_tags(tags_string: String) -> Dictionary:
	var tags = TagUtil.parse(tags_string)
	var stats = {}
	
	# 提取数值属性作为stats
	var stat_keys = ["attack", "atk", "defense", "def", "speed", "spd", "hp", "mp", "max_hp", "max_mp"]
	for key in tags:
		if key in stat_keys:
			var value = tags[key]
			if typeof(value) in [TYPE_INT, TYPE_FLOAT]:
				# 统一属性名称
				match key:
					"atk":
						stats["attack"] = value
					"def":
						stats["defense"] = value
					"spd":
						stats["speed"] = value
					_:
						stats[key] = value
	
	return stats

# ================================
# 统一查询接口
# ================================

## 统一查询函数 - 支持多种查询条件
static func query(criteria: Dictionary = {}) -> Array[InventoryManager.Item]:
	if not _initialized:
		load_database()

	var result: Array[InventoryManager.Item] = []

	# 如果没有条件，返回所有物品
	if criteria.is_empty():
		return _db.values()

	for item in _db.values():
		if _matches_criteria(item, criteria):
			result.append(item)

	return result

## 获取单个物品（保留向后兼容）
static func get_item(item_id: String) -> InventoryManager.Item:
	if not _initialized:
		load_database()
	return _db.get(item_id)

## 检查物品是否存在（保留向后兼容）
static func has_item(item_id: String) -> bool:
	if not _initialized:
		load_database()
	return _db.has(item_id)

## 内部函数：检查物品是否匹配查询条件
static func _matches_criteria(item: InventoryManager.Item, criteria: Dictionary) -> bool:
	# ID匹配
	if criteria.has("id") and item.id != criteria["id"]:
		return false

	# 类型匹配
	if criteria.has("type") and item.type != criteria["type"]:
		return false

	# 稀有度匹配
	if criteria.has("rarity") and item.rarity != criteria["rarity"]:
		return false

	# 名称搜索（模糊匹配）
	if criteria.has("name_contains"):
		var query = str(criteria["name_contains"]).to_lower()
		if not item.name.to_lower().contains(query):
			return false

	# 描述搜索（模糊匹配）
	if criteria.has("description_contains"):
		var query = str(criteria["description_contains"]).to_lower()
		if not item.description.to_lower().contains(query):
			return false

	# 标签匹配
	if criteria.has("tags"):
		var tag_criteria = criteria["tags"]
		var item_tags = item.tags if item.tags else {}
		if typeof(item_tags) == TYPE_STRING:
			item_tags = TagUtil.parse(item_tags)

		for tag_key in tag_criteria:
			var expected_value = tag_criteria[tag_key]
			if not item_tags.has(tag_key):
				return false
			if expected_value != null and item_tags[tag_key] != expected_value:
				return false

	# 值范围匹配
	if criteria.has("value_min") and item.value < criteria["value_min"]:
		return false
	if criteria.has("value_max") and item.value > criteria["value_max"]:
		return false

	return true

# ================================
# 物品实例系统
# ================================

## 创建物品实例
static func create_item_instance(item_id: String) -> InventoryManager.ItemInstance:
	if item_id.is_empty():
		print("[ItemDatabase] Error: Cannot create instance with empty item_id")
		return null
		
	var template = get_item(item_id)
	if not template:
		print("[ItemDatabase] Error: Cannot create instance, template not found: %s" % item_id)
		return null

	var instance = InventoryManager.ItemInstance.new()
	if not instance:
		print("[ItemDatabase] Error: Failed to create ItemInstance object")
		return null
		
	instance.template = template
	instance._initialize_instance_data()

	print("[ItemDatabase] Created instance for: %s" % item_id)
	return instance

# ================================
# 运行时修改接口
# ================================

## 添加新物品到数据库
static func add_item(item: InventoryManager.Item) -> bool:
	if not item or item.id.is_empty():
		print("[ItemDatabase] Error: Cannot add invalid item")
		return false

	if _db.has(item.id):
		print("[ItemDatabase] Warning: Overwriting existing item: %s" % item.id)

	_db[item.id] = item
	print("[ItemDatabase] Added item: %s" % item.id)

	return true

## 从数据库移除物品
static func remove_item(item_id: String) -> bool:
	if not _db.has(item_id):
		print("[ItemDatabase] Warning: Item not found: %s" % item_id)
		return false

	_db.erase(item_id)
	print("[ItemDatabase] Removed item: %s" % item_id)

	return true

## 热更新数据库
static func reload_database(file_path: String = "") -> bool:
	print("[ItemDatabase] Reloading database...")
	var success = load_database(file_path)
	
	return success

# ================================
# 数据导出
# ================================

## 导出数据库为JSON字符串
static func export_to_json() -> String:
	if not _initialized:
		load_database()
	
	var export_data = []
	for item in _db.values():
		var item_data = {
			"id": item.id,
			"name": item.name,
			"description": item.description,
			"icon_path": item.icon_path,
			"type": item.type,
			"stack_size": item.stack_size,
			"value": item.value,
			"rarity": item.rarity
		}
		
		# 导出tags
		if item.tags:
			var tags = item.tags
			if typeof(tags) == TYPE_DICTIONARY:
				item_data["tags"] = TagUtil.encode(tags)
			else:
				item_data["tags"] = str(tags)
		
		# 如果是装备，导出装备特有属性（通过标签）
		if item.type == InventoryManager.ItemType.EQUIPMENT:
			var level_req = TagUtil.tagi(item, "level_req", 1)
			item_data["level_requirement"] = level_req
		
		export_data.append(item_data)
	
	return JSON.stringify(export_data, "\t")

## 保存数据库到文件
static func save_to_file(file_path: String) -> bool:
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if not file:
		print("[ItemDatabase] Error: Cannot create file: %s" % file_path)
		return false
	
	var json_data = export_to_json()
	file.store_string(json_data)
	file.close()
	
	print("[ItemDatabase] Database saved to: %s" % file_path)
	return true

# ================================
# 统计和调试
# ================================

## 获取数据库统计信息
static func get_statistics() -> Dictionary:
	if not _initialized:
		load_database()
	
	var stats = {
		"total_items": _db.size(),
		"by_type": {},
		"by_rarity": {}
	}
	
	# 按类型统计
	for type_value in InventoryManager.ItemType.values():
		stats.by_type[type_value] = 0
	
	# 按稀有度统计
	for rarity in range(4):  # 0-3
		stats.by_rarity[rarity] = 0
	
	for item in _db.values():
		stats.by_type[item.type] += 1
		stats.by_rarity[item.rarity] += 1
	
	return stats

## 简化的数据库验证
static func validate_database() -> bool:
	if not _initialized:
		load_database()

	# 只检查基本的数据完整性
	for item_id in _db:
		var item = _db[item_id]
		if item.id.is_empty() or item.name.is_empty():
			print("[ItemDatabase] Invalid item found: %s" % item_id)
			return false

	return true

## 打印数据库信息
static func debug_print_database():
	if not _initialized:
		load_database()
	
	print("[ItemDatabase] === Database Status ===")
	print("  Total items: %d" % _db.size())
	print("  Data path: %s" % _data_path)
	
	var stats = get_statistics()
	print("  By type:")
	for type_value in stats.by_type:
		var type_name = InventoryManager.ItemType.keys()[type_value]
		print("    %s: %d" % [type_name, stats.by_type[type_value]])
	
	print("  By rarity:")
	for rarity in stats.by_rarity:
		print("    Rarity %d: %d" % [rarity, stats.by_rarity[rarity]])

## 打印指定物品的详细信息
static func debug_print_item(item_id: String):
	var item = get_item(item_id)
	if not item:
		print("[ItemDatabase] Item not found: %s" % item_id)
		return
	
	print("[ItemDatabase] === Item Details: %s ===" % item_id)
	print("  Name: %s" % item.name)
	print("  Description: %s" % item.description)
	print("  Type: %s" % InventoryManager.ItemType.keys()[item.type])
	print("  Stack size: %d" % item.stack_size)
	print("  Value: %d" % item.value)
	print("  Rarity: %d" % item.rarity)
	print("  Icon: %s" % item.icon_path)
	
	if item.tags:
		print("  Tags: %s" % item.tags)
	
	# 检查是否是装备类型（通过标签）
	if item.type == InventoryManager.ItemType.EQUIPMENT:
		var slot_tag = TagUtil.tags(item, "slot", "")
		var level_req = TagUtil.tagi(item, "level_req", 1)
		print("  Equipment slot: %s" % slot_tag)
		print("  Level requirement: %d" % level_req)
