extends Node

## 测试物品系统重构
## 验证模板和实例的正确分离，解决面包使用问题

func _ready():
	print("=== 物品系统重构测试 ===")
	test_template_immutability()
	test_instance_independence()
	test_bread_usage_problem()
	test_inventory_operations()
	print("=== 测试完成 ===")

## 测试1：模板不可变性
func test_template_immutability():
	print("\n--- 测试1：模板不可变性 ---")
	
	# 初始化数据库
	ItemDatabase.initialize()
	
	# 获取面包模板
	var bread_template = ItemDatabase.get_item("bread")
	if not bread_template:
		print("❌ 无法获取bread模板")
		return
	
	var original_use = TagUtil.tagi(bread_template, "use", 0)
	print("✓ 面包模板原始use值: %d" % original_use)
	
	# 创建实例并使用
	var instance1 = ItemDatabase.create_item_instance("bread")
	var instance2 = ItemDatabase.create_item_instance("bread")
	
	if not instance1 or not instance2:
		print("❌ 无法创建面包实例")
		return
	
	print("✓ 实例1初始use值: %d" % instance1.get_remaining_uses())
	print("✓ 实例2初始use值: %d" % instance2.get_remaining_uses())
	
	# 使用实例1
	instance1.consume_use()
	print("✓ 实例1使用后use值: %d" % instance1.get_remaining_uses())
	print("✓ 实例2状态: %d" % instance2.get_remaining_uses())
	
	# 验证模板未被修改
	var template_use_after = TagUtil.tagi(bread_template, "use", 0)
	if template_use_after == original_use:
		print("✅ 模板保持不变: %d" % template_use_after)
	else:
		print("❌ 模板被意外修改: %d -> %d" % [original_use, template_use_after])

## 测试2：实例独立性
func test_instance_independence():
	print("\n--- 测试2：实例独立性 ---")
	
	# 创建多个面包实例
	var bread1 = ItemDatabase.create_item_instance("bread")
	var bread2 = ItemDatabase.create_item_instance("bread")
	var bread3 = ItemDatabase.create_item_instance("bread")
	
	if not bread1 or not bread2 or not bread3:
		print("❌ 无法创建面包实例")
		return
	
	print("✓ 面包1初始状态: %d uses" % bread1.get_remaining_uses())
	print("✓ 面包2初始状态: %d uses" % bread2.get_remaining_uses())
	print("✓ 面包3初始状态: %d uses" % bread3.get_remaining_uses())
	
	# 只使用面包1
	bread1.consume_use()
	
	print("✓ 面包1使用后: %d uses" % bread1.get_remaining_uses())
	print("✓ 面包2状态: %d uses" % bread2.get_remaining_uses())
	print("✓ 面包3状态: %d uses" % bread3.get_remaining_uses())
	
	# 验证实例独立性
	if bread1.get_remaining_uses() != bread2.get_remaining_uses():
		print("✅ 实例状态独立")
	else:
		print("❌ 实例状态相互影响")

## 测试3：面包使用问题修复
func test_bread_usage_problem():
	print("\n--- 测试3：面包使用问题修复 ---")
	
	# 清空背包
	InventoryManager.clear_inventory()
	
	# 添加3个面包到背包
	InventoryManager.add_item("bread", 3)
	print("✓ 添加了3个面包到背包")
	
	# 检查背包状态
	var slots = InventoryManager.get_inventory_slots()
	var bread_slot = null
	for slot in slots:
		if not slot.is_empty() and slot.template_id == "bread":
			bread_slot = slot
			break
	
	if not bread_slot:
		print("❌ 背包中没有找到面包")
		return
	
	print("✓ 背包中面包数量: %d" % bread_slot.get_quantity())
	
	# 显示每个实例的状态
	for i in range(bread_slot.item_instances.size()):
		var instance = bread_slot.item_instances[i]
		print("  面包实例%d: 剩余使用次数=%d" % [i, instance.get_remaining_uses()])
	
	# 使用第一个面包
	print("\n使用第一个面包...")
	var slot_index = 0
	for i in range(slots.size()):
		if slots[i] == bread_slot:
			slot_index = i
			break
	
	InventoryManager.use_item(slot_index)
	
	# 检查使用后的状态
	print("✓ 使用后面包数量: %d" % bread_slot.get_quantity())
	
	# 显示剩余实例的状态
	for i in range(bread_slot.item_instances.size()):
		var instance = bread_slot.item_instances[i]
		print("  面包实例%d: 剩余使用次数=%d" % [i, instance.get_remaining_uses()])
	
	# 验证其他面包仍然可用
	if bread_slot.get_quantity() > 0:
		var first_instance = bread_slot.get_first_instance()
		if first_instance and first_instance.is_usable():
			print("✅ 其他面包仍然可用")
		else:
			print("❌ 其他面包无法使用")
	else:
		print("❌ 所有面包都被移除了")

## 测试4：背包操作
func test_inventory_operations():
	print("\n--- 测试4：背包操作 ---")
	
	# 清空背包
	InventoryManager.clear_inventory()
	
	# 添加不同类型的物品
	InventoryManager.add_item("bread", 2)
	InventoryManager.add_item("potion_health", 1)
	
	print("✓ 添加了物品到背包")
	
	# 打印背包内容
	InventoryManager.print_inventory()
	
	# 测试TagUtil兼容性
	var slots = InventoryManager.get_inventory_slots()
	for slot in slots:
		if not slot.is_empty():
			var template = slot.get_template()
			var instance = slot.get_first_instance()
			
			print("模板 %s:" % template.name)
			print("  TagUtil.is_usable(template): %s" % TagUtil.is_usable(template))
			print("  TagUtil.is_usable(instance): %s" % TagUtil.is_usable(instance))
			print("  instance.is_usable(): %s" % instance.is_usable())
	
	print("✅ 背包操作测试完成")
