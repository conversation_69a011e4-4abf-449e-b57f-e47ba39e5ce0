[{"id": "potion_health", "name": "生命药水", "description": "恢复50点生命值", "icon_path": "res://Asset/Icon/consumable_potion_health.png", "type": 0, "stack_size": 50, "value": 50, "rarity": 1, "tags": "use:1,hp:50"}, {"id": "potion_mana", "name": "魔法药水", "description": "恢复30点魔法值", "icon_path": "res://Asset/Icon/consumable_potion_mana.png", "type": 0, "stack_size": 30, "value": 30, "rarity": 1, "tags": "use:1,mp:30"}, {"id": "bread", "name": "面包", "description": "恢复少量生命值", "icon_path": "res://Asset/Icon/consumable_bread.png", "type": 0, "stack_size": 10, "value": 10, "rarity": 0, "tags": "use:1,hp:10"}, {"id": "sword_iron", "name": "铁剑", "description": "基础的铁制剑", "icon_path": "res://Asset/Icon/equipment_sword_iron.png", "type": 1, "stack_size": 1, "value": 100, "rarity": 1, "tags": "slot:weapon,attack:10"}, {"id": "armor_leather", "name": "皮甲", "description": "基础的皮制护甲", "icon_path": "res://Asset/Icon/equipment_armor_leather.png", "type": 1, "stack_size": 1, "value": 80, "rarity": 0, "tags": "slot:armor,defense:5,hp:20"}, {"id": "boots_leather", "name": "皮靴", "description": "基础的皮制靴子", "icon_path": "res://Asset/Icon/equipment_boots_leather.png", "type": 1, "stack_size": 1, "value": 40, "rarity": 0, "tags": "slot:shoes,speed:2,defense:2"}, {"id": "iron_ore", "name": "铁矿石", "description": "用于锻造的基础材料", "icon_path": "res://Asset/Icon/material_iron_ore.png", "type": 2, "stack_size": 50, "value": 10, "rarity": 0, "tags": "value:10"}, {"id": "leather", "name": "皮革", "description": "用于制作装备的材料", "icon_path": "res://Asset/Icon/material_leather.png", "type": 2, "stack_size": 20, "value": 5, "rarity": 0, "tags": "value:5"}, {"id": "school_key", "name": "学校钥匙", "description": "打开学校大门的钥匙", "icon_path": "res://Asset/Icon/key_school_key.png", "type": 3, "stack_size": 1, "value": 0, "rarity": 2, "tags": "quest_id:school_entrance"}, {"id": "potion_full_heal", "name": "完全治疗药水", "description": "完全恢复生命值和魔法值", "icon_path": "res://Asset/Icon/consumable_potion_full_heal.png", "type": 0, "stack_size": 5, "value": 200, "rarity": 3, "tags": "use:1,hp:999,mp:999"}, {"id": "sword_steel", "name": "钢剑", "description": "优质的钢制剑，比铁剑更锋利", "icon_path": "res://Asset/Icon/equipment_sword_steel.png", "type": 1, "stack_size": 1, "value": 250, "rarity": 2, "tags": "slot:weapon,attack:18,speed:1"}, {"id": "armor_chainmail", "name": "锁子甲", "description": "坚固的金属护甲", "icon_path": "res://Asset/Icon/equipment_armor_chainmail.png", "type": 1, "stack_size": 1, "value": 180, "rarity": 2, "tags": "slot:armor,defense:12,hp:35,speed:-1"}, {"id": "helmet_iron", "name": "铁盔", "description": "基础的铁制头盔", "icon_path": "res://Asset/Icon/equipment_helmet_iron.png", "type": 1, "stack_size": 1, "value": 60, "rarity": 1, "tags": "slot:helmet,defense:3,hp:10"}, {"id": "ring_power", "name": "力量戒指", "description": "增加攻击力的魔法戒指", "icon_path": "res://Asset/Icon/equipment_ring_power.png", "type": 1, "stack_size": 1, "value": 150, "rarity": 2, "tags": "slot:accessory,attack:8"}, {"id": "boots_speed", "name": "疾风靴", "description": "大幅提升移动速度的魔法靴子", "icon_path": "res://Asset/Icon/equipment_boots_speed.png", "type": 1, "stack_size": 1, "value": 120, "rarity": 2, "tags": "slot:shoes,speed:8,defense:1"}, {"id": "gem_ruby", "name": "红宝石", "description": "珍贵的红色宝石，可用于强化装备", "icon_path": "res://Asset/Icon/material_gem_ruby.png", "type": 2, "stack_size": 10, "value": 500, "rarity": 3, "tags": "value:500,rarity:3"}, {"id": "scroll_teleport", "name": "传送卷轴", "description": "瞬间传送到安全地点", "icon_path": "res://Asset/Icon/consumable_scroll_teleport.png", "type": 0, "stack_size": 5, "value": 100, "rarity": 2, "tags": "use:1,quest_id:teleport_home"}, {"id": "elixir_strength", "name": "力量药剂", "description": "临时大幅提升攻击力30秒", "icon_path": "res://Asset/Icon/consumable_elixir_strength.png", "type": 0, "stack_size": 10, "value": 80, "rarity": 2, "tags": "use:1,temp_buff:attack:15:30"}, {"id": "coin_gold", "name": "金币", "description": "闪闪发光的金币", "icon_path": "res://Asset/Icon/misc_coin_gold.png", "type": 4, "stack_size": 999, "value": 1, "rarity": 0, "tags": "use:1,coins:1"}, {"id": "artifact_ancient", "name": "远古神器", "description": "蕴含神秘力量的远古物品", "icon_path": "res://Asset/Icon/key_artifact_ancient.png", "type": 3, "stack_size": 1, "value": 9999, "rarity": 3, "tags": "quest_id:ancient_mystery,value:9999"}]